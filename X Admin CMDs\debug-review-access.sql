-- Debug review access for completed applications
USE u618120801_SplitJob_33;

-- Check current status of applications and contracts
SELECT 'Current Application and Contract Status:' as info;
SELECT 
    a.id as app_id,
    a.status as app_status,
    c.id as contract_id,
    c.status as contract_status,
    c.offer_letter_released,
    c.offer_letter_url,
    CASE 
        WHEN a.status = 'completed' THEN 'Review Available'
        ELSE 'Review Not Available'
    END as review_status
FROM applications a
LEFT JOIN contracts c ON c.application_id = a.id
WHERE a.id IN (1, 2, 3, 6)
ORDER BY a.id;

-- Check if there are any existing reviews
SELECT 'Existing Reviews:' as info;
SELECT 
    r.id,
    r.application_id,
    r.reviewer_id,
    r.reviewed_user_id,
    r.rating,
    reviewer.full_name as reviewer_name,
    reviewed.full_name as reviewed_name,
    r.created_at
FROM reviews r
JOIN users reviewer ON r.reviewer_id = reviewer.id
JOIN users reviewed ON r.reviewed_user_id = reviewed.id
WHERE r.application_id IN (1, 2, 3, 6)
ORDER BY r.application_id, r.created_at;

-- Check specific contract 6 details
SELECT 'Contract 6 Full Details:' as info;
SELECT 
    a.id as app_id,
    a.status as app_status,
    c.id as contract_id,
    c.status as contract_status,
    c.seeker_signed,
    c.referrer_signed,
    c.seeker_documents_verified,
    c.referrer_documents_verified,
    c.offer_letter_released,
    c.offer_letter_url,
    jp.user_id as seeker_id,
    a.referrer_id
FROM applications a
JOIN contracts c ON c.application_id = a.id
JOIN job_posts jp ON a.job_post_id = jp.id
WHERE c.id = 6;
