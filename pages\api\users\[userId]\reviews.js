import { query } from "../../../../lib/db";

export default async function handler(req, res) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { userId } = req.query;
  const { page = 1, limit = 10 } = req.query;

  if (!userId) {
    return res.status(400).json({ error: "User ID is required" });
  }

  try {
    // Get user basic info with ratings
    const users = await query(`
      SELECT
        u.id,
        u.full_name,
        u.user_type,
        u.bio,
        u.linkedin_url,
        u.github_url,
        u.portfolio_url,
        u.created_at,
        COUNT(DISTINCT r.id) as total_reviews,
        AVG(r.rating) as average_rating
      FROM users u
      LEFT JOIN reviews r ON u.id = r.reviewed_user_id
      WHERE u.id = ?
      GROUP BY u.id
    `, [userId]);

    if (users.length === 0) {
      return res.status(404).json({ error: "User not found" });
    }

    const user = users[0];

    // Get paginated reviews
    const offset = (page - 1) * limit;
    const reviews = await query(`
      SELECT
        r.*,
        reviewer.full_name as reviewer_name,
        reviewer.user_type as reviewer_type,
        jp.title as job_title
      FROM reviews r
      JOIN users reviewer ON r.reviewer_id = reviewer.id
      LEFT JOIN applications a ON r.application_id = a.id
      LEFT JOIN job_posts jp ON a.job_post_id = jp.id
      WHERE r.reviewed_user_id = ?
      ORDER BY r.created_at DESC
      LIMIT ? OFFSET ?
    `, [userId, parseInt(limit), offset]);

    // Get total count for pagination
    const countResult = await query(`
      SELECT COUNT(*) as total
      FROM reviews
      WHERE reviewed_user_id = ?
    `, [userId]);

    const totalReviews = countResult[0].total;
    const totalPages = Math.ceil(totalReviews / limit);

    // Calculate rating distribution
    const ratingDistribution = await query(`
      SELECT 
        rating,
        COUNT(*) as count
      FROM reviews
      WHERE reviewed_user_id = ?
      GROUP BY rating
      ORDER BY rating DESC
    `, [userId]);

    const distribution = {};
    for (let i = 1; i <= 5; i++) {
      distribution[i] = 0;
    }
    ratingDistribution.forEach(item => {
      distribution[item.rating] = item.count;
    });

    res.status(200).json({
      user: {
        ...user,
        average_rating: user.average_rating ? parseFloat(user.average_rating).toFixed(1) : null
      },
      reviews,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalReviews,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      },
      ratingDistribution: distribution
    });
  } catch (error) {
    console.error("Failed to fetch user reviews:", error);
    res.status(500).json({ error: "Failed to fetch user reviews" });
  }
}
