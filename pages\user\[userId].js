import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import Layout from "../../components/Layout";
import UserProfile from "../../components/UserProfile";
import { ArrowLeft } from "lucide-react";

export default function UserProfilePage({ user }) {
  const router = useRouter();
  const { userId } = router.query;
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (router.isReady) {
      setLoading(false);
    }
  }, [router.isReady]);

  if (loading) {
    return (
      <Layout user={user}>
        <div className="min-h-screen bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 py-8">
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading...</p>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (!userId) {
    return (
      <Layout user={user}>
        <div className="min-h-screen bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 py-8">
            <div className="text-center py-12">
              <p className="text-gray-600">Invalid user ID</p>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout user={user}>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 py-8">
          {/* Back Button */}
          <div className="mb-6">
            <button
              onClick={() => router.back()}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              <ArrowLeft size={16} />
              Back
            </button>
          </div>

          {/* User Profile Component */}
          <UserProfile userId={userId} />
        </div>
      </div>
    </Layout>
  );
}

// This function gets called at build time for static generation
export async function getStaticPaths() {
  // We'll return empty paths and enable fallback
  // This allows pages to be generated on-demand
  return {
    paths: [],
    fallback: 'blocking'
  };
}

// This function gets called at build time for static generation
export async function getStaticProps({ params }) {
  const { userId } = params;

  // Validate userId is a number
  if (!userId || isNaN(parseInt(userId))) {
    return {
      notFound: true,
    };
  }

  return {
    props: {
      userId: parseInt(userId),
    },
    // Regenerate the page at most once every hour
    revalidate: 3600,
  };
}
