-- Debug contract 6 for offer letter upload issue
USE u618120801_SplitJob_33;

-- Check contract 6 details
SELECT 'Contract 6 Details:' as info;
SELECT 
    c.id,
    c.application_id,
    c.status,
    c.seeker_signed,
    c.referrer_signed,
    c.seeker_documents_verified,
    c.referrer_documents_verified,
    c.offer_letter_released,
    a.id as app_id,
    jp.user_id as seeker_id,
    a.referrer_id
FROM contracts c
JOIN applications a ON c.application_id = a.id
JOIN job_posts jp ON a.job_post_id = jp.id
WHERE c.id = 6;

-- Check if contract 6 meets the offer letter upload criteria
SELECT 'Contract 6 Upload Criteria Check:' as info;
SELECT 
    c.id,
    c.status,
    c.seeker_documents_verified,
    c.referrer_documents_verified,
    CASE 
        WHEN c.status = 'documents_verified' THEN 'Status OK'
        WHEN c.status = 'offer_released' THEN 'Already released'
        WHEN c.status = '' OR c.status IS NULL THEN 'Empty status'
        ELSE 'Other status'
    END as status_check,
    CASE 
        WHEN c.seeker_documents_verified = 1 AND c.referrer_documents_verified = 1 THEN 'Documents OK'
        ELSE 'Documents not verified'
    END as documents_check
FROM contracts c
WHERE c.id = 6;
