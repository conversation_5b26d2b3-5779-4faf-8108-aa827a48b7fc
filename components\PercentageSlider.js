import { useState, useEffect } from 'react';

export default function PercentageSlider({ 
  value, 
  onChange, 
  className = "",
  disabled = false 
}) {
  const [localValue, setLocalValue] = useState(value || 0);

  useEffect(() => {
    setLocalValue(value || 0);
  }, [value]);

  const handleChange = (e) => {
    const newValue = parseInt(e.target.value);
    setLocalValue(newValue);
    onChange(newValue);
  };

  const handleReset = () => {
    setLocalValue(0);
    onChange(0);
  };

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-700">
          Percentage: {localValue}%
        </label>
        {localValue > 0 && (
          <button
            onClick={handleReset}
            className="text-xs text-blue-600 hover:text-blue-800"
          >
            Reset
          </button>
        )}
      </div>
      
      <div className="relative">
        <input
          type="range"
          min="0"
          max="100"
          step="1"
          value={localValue}
          onChange={handleChange}
          disabled={disabled}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
        />
        
        {/* Custom slider styling */}
        <style jsx>{`
          .slider::-webkit-slider-thumb {
            appearance: none;
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: #DC143C;
            cursor: pointer;
            border: 2px solid #ffffff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          }
          
          .slider::-webkit-slider-thumb:hover {
            background: #B91C3C;
            transform: scale(1.1);
          }
          
          .slider::-moz-range-thumb {
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: #DC143C;
            cursor: pointer;
            border: 2px solid #ffffff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          }
          
          .slider::-moz-range-thumb:hover {
            background: #B91C3C;
            transform: scale(1.1);
          }
          
          .slider::-webkit-slider-track {
            height: 8px;
            border-radius: 4px;
            background: linear-gradient(
              to right,
              #DC143C 0%,
              #DC143C ${(localValue / 100) * 100}%,
              #e5e7eb ${(localValue / 100) * 100}%,
              #e5e7eb 100%
            );
          }
          
          .slider::-moz-range-track {
            height: 8px;
            border-radius: 4px;
            background: linear-gradient(
              to right,
              #DC143C 0%,
              #DC143C ${(localValue / 100) * 100}%,
              #e5e7eb ${(localValue / 100) * 100}%,
              #e5e7eb 100%
            );
          }
          
          .slider:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        `}</style>
      </div>
      
      {/* Percentage markers */}
      <div className="flex justify-between text-xs text-gray-500 px-1">
        <span>0%</span>
        <span>25%</span>
        <span>50%</span>
        <span>75%</span>
        <span>100%</span>
      </div>
      
      {localValue > 0 ? (
        <div className="text-xs text-white bg-[#DC143C] p-2 rounded font-medium">
          ✓ Filtering: Exactly {localValue}% commission
        </div>
      ) : (
        <div className="text-xs text-gray-500 p-2 rounded border border-dashed">
          Move slider to filter by specific percentage
        </div>
      )}
    </div>
  );
}
