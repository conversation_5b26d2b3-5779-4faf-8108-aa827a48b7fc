import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import Layout from "../../components/Layout";
import ContractViewer from "../../components/ContractViewer";
import PeerDocumentVerification from "../../components/PeerDocumentVerification";
import DocumentReviewModal from "../../components/DocumentReviewModal";
import ReviewForm from "../../components/ReviewForm";
import OfferLetterUpload from "../../components/OfferLetterUpload";
import { FileText, Shield, Star, CheckCircle, Download, Eye } from "lucide-react";

export default function ContractPage({ user }) {
  const router = useRouter();
  const { id, tab } = router.query;
  const [application, setApplication] = useState(null);
  const [contract, setContract] = useState(null);
  const [activeTab, setActiveTab] = useState(tab || "contract");
  const [loading, setLoading] = useState(true);
  const [showDocumentReview, setShowDocumentReview] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [releasingOffer, setReleasingOffer] = useState(false);

  useEffect(() => {
    if (id && user) {
      fetchApplicationData();
    }
  }, [id, user]);

  useEffect(() => {
    if (tab && ["contract", "documents", "review"].includes(tab)) {
      setActiveTab(tab);
    }
  }, [tab]);

  const fetchApplicationData = async () => {
    try {
      // Fetch contract details (id is contract ID)
      const contractRes = await fetch(`/api/contracts/${id}`);

      if (contractRes.ok) {
        const contractData = await contractRes.json();
        setContract(contractData.contract);

        // Create application object from contract data for compatibility
        setApplication({
          id: contractData.contract.application_id,
          job_post_id: contractData.contract.job_post_id,
          referrer_id: contractData.contract.referrer_id,
          job_title: contractData.contract.job_title,
          company_name: contractData.contract.company_name,
          status: 'accepted', // If contract exists, application must be accepted
          contract_id: contractData.contract.id
        });
      } else {
        console.error("Failed to fetch contract");
        router.push('/dashboard');
      }
    } catch (error) {
      console.error("Failed to fetch data:", error);
      router.push('/dashboard');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyDocuments = async () => {
    setVerifying(true);
    try {
      const res = await fetch("/api/contracts/verify-documents", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          contractId: contract.id,
          action: "verify"
        }),
      });

      if (res.ok) {
        const data = await res.json();
        alert(data.message);
        fetchApplicationData(); // Refresh contract data
      } else {
        const error = await res.json();
        alert(error.error || "Failed to verify documents");
      }
    } catch (error) {
      alert("Failed to verify documents");
      console.error("Failed to verify documents:", error);
    } finally {
      setVerifying(false);
    }
  };

  const handleReleaseOffer = async () => {
    const confirmed = window.confirm(
      "Are you sure you want to release the offer letter? This action cannot be undone."
    );

    if (!confirmed) return;

    setReleasingOffer(true);
    try {
      const res = await fetch("/api/contracts/release-offer", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ contractId: contract.id }),
      });

      if (res.ok) {
        const data = await res.json();
        alert("Offer letter released successfully!");

        // Open the offer letter
        window.open(data.offerLetterUrl, '_blank');

        fetchApplicationData(); // Refresh contract data
      } else {
        const error = await res.json();
        alert(error.error || "Failed to release offer letter");
      }
    } catch (error) {
      alert("Failed to release offer letter");
      console.error("Failed to release offer letter:", error);
    } finally {
      setReleasingOffer(false);
    }
  };

  if (loading) {
    return (
      <Layout user={user}>
        <div className="min-h-screen flex items-center justify-center">
          <div>Loading...</div>
        </div>
      </Layout>
    );
  }

  if (!application || !contract) {
    return (
      <Layout user={user}>
        <div className="min-h-screen flex items-center justify-center">
          <div>Application not found</div>
        </div>
      </Layout>
    );
  }

  const tabs = [
    { id: "contract", label: "Contract", icon: FileText },
    { id: "documents", label: "Documents", icon: Shield },
    { id: "review", label: "Review", icon: Star },
  ];

  const canAccessDocuments = contract.status === "signed" || contract.status === "documents_pending" || contract.status === "documents_verified" || contract.status === "offer_released";
  const canLeaveReview = application.status === "completed" ||
                         (contract.offer_letter_released && contract.seeker_signed && contract.referrer_signed);

  // Debug logging for review access
  console.log("Review access debug:", {
    applicationStatus: application.status,
    offerLetterReleased: contract.offer_letter_released,
    seekerSigned: contract.seeker_signed,
    referrerSigned: contract.referrer_signed,
    canLeaveReview: canLeaveReview
  });
  const canVerifyDocuments = canAccessDocuments && !contract.seeker_documents_verified && !contract.referrer_documents_verified;
  const hasVerifiedDocuments = contract.seeker_documents_verified && contract.referrer_documents_verified;
  const canReleaseOffer = hasVerifiedDocuments && !contract.offer_letter_released;

  const isSeeker = user.user_type === "seeker";
  // Cross-party verification: seekers verify referrer docs, referrers verify seeker docs
  const hasUserVerifiedOtherParty = isSeeker ? contract.referrer_documents_verified : contract.seeker_documents_verified;
  const otherPartyHasVerifiedUser = isSeeker ? contract.seeker_documents_verified : contract.referrer_documents_verified;

  return (
    <Layout user={user}>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <h1 className="text-2xl font-bold mb-6">
            Application: {application.job_title}
          </h1>

          {/* Status Badge */}
          <div className="mb-6">
            <span
              className={`px-3 py-1 rounded-full text-sm font-medium ${
                application.status === "completed"
                  ? "bg-green-100 text-green-800"
                  : "bg-blue-100 text-blue-800"
              }`}
            >
              {application.status.replace("_", " ")}
            </span>
          </div>

          {/* Tabs */}
          <div className="bg-white rounded-lg shadow mb-6">
            <div className="border-b">
              <div className="flex">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  const isDisabled =
                    (tab.id === "documents" && !canAccessDocuments) ||
                    (tab.id === "review" && !canLeaveReview);

                  return (
                    <button
                      key={tab.id}
                      onClick={() => !isDisabled && setActiveTab(tab.id)}
                      disabled={isDisabled}
                      className={`flex-1 px-4 py-4 font-medium flex items-center justify-center gap-2 transition-colors ${
                        activeTab === tab.id
                          ? "border-b-2 border-black text-black"
                          : isDisabled
                          ? "text-gray-300 cursor-not-allowed"
                          : "text-gray-500 hover:text-gray-700"
                      }`}
                    >
                      <Icon size={20} />
                      {tab.label}
                    </button>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Tab Content */}
          {activeTab === "contract" && (
            <ContractViewer
              contract={contract}
              user={user}
              onSign={fetchApplicationData}
            />
          )}

          {activeTab === "documents" && canAccessDocuments && (
            <div className="space-y-6">
              {/* Peer Document Verification */}
              <PeerDocumentVerification
                contractId={contract.id}
                peerUserId={user.user_type === "seeker" ? contract.referrer_id : contract.seeker_id}
                currentUser={user}
                onVerificationComplete={fetchApplicationData}
              />

              {/* Verification Status */}
              <div className="card">
                <div className="card-header">
                  <h3 className="card-title flex items-center gap-2">
                    <CheckCircle size={20} />
                    Verification Status
                  </h3>
                </div>
                <div className="card-content">
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      {contract.seeker_documents_verified ? (
                        <CheckCircle className="text-green-500" size={20} />
                      ) : (
                        <div className="w-5 h-5 border-2 border-gray-300 rounded-full"></div>
                      )}
                      <span className="text-sm">Job Seeker Documents: {contract.seeker_documents_verified ? "Verified by Referrer" : "Pending Verification"}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      {contract.referrer_documents_verified ? (
                        <CheckCircle className="text-green-500" size={20} />
                      ) : (
                        <div className="w-5 h-5 border-2 border-gray-300 rounded-full"></div>
                      )}
                      <span className="text-sm">Referrer Documents: {contract.referrer_documents_verified ? "Verified by Job Seeker" : "Pending Verification"}</span>
                    </div>
                  </div>

                  {/* Offer Letter Release */}
                  {contract.seeker_documents_verified &&
                   contract.referrer_documents_verified &&
                   !contract.offer_letter_released &&
                   user.user_type === "seeker" && (
                    <OfferLetterUpload
                      contractId={contract.id}
                      onSuccess={(data) => {
                        // Refresh the page to show updated status
                        window.location.reload();
                      }}
                      className="mt-6"
                    />
                  )}

                  {contract.offer_letter_released && (
                    <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <h4 className="font-medium text-blue-800 mb-2">Offer Letter Released</h4>
                      <p className="text-sm text-blue-700 mb-3">
                        The offer letter has been released. The transaction is now complete!
                      </p>
                      <button
                        onClick={() => window.open(contract.offer_letter_url, '_blank')}
                        className="btn-outline btn-sm flex items-center gap-2"
                      >
                        <Download size={16} />
                        Download Offer Letter
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === "review" && canLeaveReview && (
            <ReviewForm
              applicationId={application.id}
              onSubmit={() => router.push("/dashboard")}
            />
          )}

          {/* Tab Instructions */}
          {activeTab === "documents" && !canAccessDocuments && (
            <div className="card">
              <div className="card-content text-center py-8">
                <Shield size={48} className="mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium mb-2">Contract Required</h3>
                <p className="text-gray-600">
                  Both parties must sign the contract before proceeding with
                  document exchange.
                </p>
              </div>
            </div>
          )}

          {activeTab === "review" && !canLeaveReview && (
            <div className="card">
              <div className="card-content text-center py-8">
                <Star size={48} className="mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium mb-2">
                  Transaction Not Complete
                </h3>
                <p className="text-gray-600">
                  You can leave a review once the referral process is completed.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Document Review Modal */}
      <DocumentReviewModal
        isOpen={showDocumentReview}
        onClose={() => setShowDocumentReview(false)}
        applicationId={application?.id}
        onVerify={handleVerifyDocuments}
        currentUser={user}
        targetUserType={isSeeker ? "referrer" : "seeker"}
      />
    </Layout>
  );
}
