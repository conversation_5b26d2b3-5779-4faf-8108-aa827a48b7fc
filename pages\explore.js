import { useState, useEffect, useCallback } from "react";
import Layout from "../components/Layout";
import JobCard from "../components/JobCard";
import FilterSidebar from "../components/FilterSidebar";
import Pagination from "../components/Pagination";
import useInfiniteScroll from "../hooks/useInfiniteScroll";
import { Search, Filter, Grid, List, RotateCcw } from "lucide-react";

export default function Explore({ user }) {
  const [jobs, setJobs] = useState([]);
  const [filteredJobs, setFilteredJobs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState("pagination"); // "pagination" or "infinite"
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalJobs: 0,
    hasNextPage: false
  });
  const [filters, setFilters] = useState({
    userType: "all",
    paymentType: "percentage",
    jobRole: "all",
    percentageValue: 0,
    minFixed: "",
    maxFixed: "",
  });

  useEffect(() => {
    fetchJobs(1, true);
  }, []);

  useEffect(() => {
    applyFilters();
  }, [jobs, filters, searchTerm]);

  // Trigger API call when filters or search term change
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchJobs(1, true);
    }, 300); // Debounce filter changes

    return () => clearTimeout(timeoutId);
  }, [filters, searchTerm]);

  const fetchJobs = async (page = 1, reset = false) => {
    if (page === 1) {
      setLoading(true);
    } else {
      setLoadingMore(true);
    }

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "12",
        search: searchTerm,
        userType: filters.userType,
        paymentType: filters.paymentType,
        jobRole: filters.jobRole,
        percentageValue: filters.percentageValue,
        minFixed: filters.minFixed,
        maxFixed: filters.maxFixed
      });

      console.log('Fetching jobs with filters:', {
        searchTerm,
        filters,
        url: `/api/jobs?${params}`
      });

      const res = await fetch(`/api/jobs?${params}`);
      const data = await res.json();

      if (reset || page === 1) {
        setJobs(data.jobs || []);
      } else {
        setJobs(prev => [...prev, ...(data.jobs || [])]);
      }

      setPagination(data.pagination || {
        currentPage: 1,
        totalPages: 1,
        totalJobs: 0,
        hasNextPage: false
      });
    } catch (error) {
      console.error("Failed to fetch jobs:", error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const fetchMoreJobs = useCallback(async () => {
    if (pagination.hasNextPage && !loadingMore) {
      await fetchJobs(pagination.currentPage + 1, false);
    }
  }, [pagination.hasNextPage, pagination.currentPage, loadingMore]);

  const handlePageChange = (page) => {
    fetchJobs(page, true);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleSearch = () => {
    fetchJobs(1, true);
  };

  const resetFilters = () => {
    setFilters({
      userType: "all",
      paymentType: "percentage",
      jobRole: "all",
      percentageValue: 0,
      minFixed: "",
      maxFixed: "",
    });
    setSearchTerm("");
    fetchJobs(1, true);
  };

  const applyFilters = () => {
    // Since server-side filtering is now handling most filters,
    // we only need client-side filtering for real-time search
    let filtered = [...jobs];

    // Only apply client-side search if we're not using server-side search
    // This provides immediate feedback while typing
    if (searchTerm && searchTerm.length < 3) {
      filtered = filtered.filter(
        (job) =>
          job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          job.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          job.job_role.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredJobs(filtered);
  };

  // Infinite scroll hook
  const { isFetching } = useInfiniteScroll({
    fetchMore: fetchMoreJobs,
    hasNextPage: viewMode === "infinite" && pagination.hasNextPage,
    threshold: 100
  });

  return (
    <Layout user={user}>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-4">Explore Opportunities</h1>

            {/* Search Bar */}
            <div className="flex gap-4 mb-6">
              <div className="flex-1 relative">
                <Search
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                  size={20}
                />
                <input
                  type="text"
                  placeholder="Search by title, role, or description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="w-full pl-10 pr-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  style={{ backgroundColor: '#F9FAFB' }}
                />
              </div>
              <button
                onClick={handleSearch}
                className="px-6 py-3 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors"
              >
                Search
              </button>
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="px-6 py-3 border rounded-lg hover:bg-gray-100 transition-colors flex items-center gap-2 md:hidden"
              >
                <Filter size={20} />
                Filters
              </button>
            </div>

            {/* Controls */}
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center gap-4">
                <span className="text-sm text-gray-600">
                  {pagination.totalJobs} opportunities found
                </span>
                <button
                  onClick={resetFilters}
                  className="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1"
                >
                  <RotateCcw size={14} />
                  Reset Filters
                </button>
              </div>

              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">View:</span>
                <div className="flex border rounded-lg overflow-hidden">
                  <button
                    onClick={() => setViewMode("pagination")}
                    className={`px-3 py-2 text-sm flex items-center gap-1 ${
                      viewMode === "pagination"
                        ? "bg-black text-white"
                        : "bg-white text-gray-700 hover:bg-gray-50"
                    }`}
                  >
                    <Grid size={14} />
                    Pages
                  </button>
                  <button
                    onClick={() => setViewMode("infinite")}
                    className={`px-3 py-2 text-sm flex items-center gap-1 ${
                      viewMode === "infinite"
                        ? "bg-black text-white"
                        : "bg-white text-gray-700 hover:bg-gray-50"
                    }`}
                  >
                    <List size={14} />
                    Scroll
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div className="flex gap-8">
            {/* Filters Sidebar - Desktop */}
            <div className="hidden md:block w-64">
              <FilterSidebar filters={filters} setFilters={setFilters} />
            </div>

            {/* Mobile Filters */}
            {showFilters && (
              <div className="fixed inset-0 bg-black bg-opacity-50 z-50 md:hidden">
                <div className="bg-white w-80 h-full overflow-y-auto">
                  <div className="p-4 border-b flex justify-between items-center">
                    <h2 className="text-lg font-semibold">Filters</h2>
                    <button onClick={() => setShowFilters(false)}>✕</button>
                  </div>
                  <div className="p-4">
                    <FilterSidebar filters={filters} setFilters={setFilters} />
                  </div>
                </div>
              </div>
            )}

            {/* Job Listings */}
            <div className="flex-1">
              {loading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                  <p className="mt-2 text-gray-600">Loading opportunities...</p>
                </div>
              ) : filteredJobs.length === 0 ? (
                <div className="text-center py-12 text-gray-500">
                  <p className="text-lg mb-2">No opportunities found</p>
                  <p className="text-sm">Try adjusting your search criteria or filters.</p>
                </div>
              ) : (
                <>
                  <div className="grid gap-4">
                    {filteredJobs.map((job) => (
                      <JobCard key={job.id} job={job} user={user} />
                    ))}
                  </div>

                  {/* Infinite Scroll Loading */}
                  {viewMode === "infinite" && (isFetching || loadingMore) && (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 mx-auto"></div>
                      <p className="mt-2 text-sm text-gray-600">Loading more...</p>
                    </div>
                  )}

                  {/* Pagination */}
                  {viewMode === "pagination" && pagination.totalPages > 1 && (
                    <div className="mt-8">
                      <Pagination
                        currentPage={pagination.currentPage}
                        totalPages={pagination.totalPages}
                        onPageChange={handlePageChange}
                        totalItems={pagination.totalJobs}
                        itemsPerPage={12}
                      />
                    </div>
                  )}

                  {/* End of results for infinite scroll */}
                  {viewMode === "infinite" && !pagination.hasNextPage && filteredJobs.length > 0 && (
                    <div className="text-center py-8 text-gray-500">
                      <p className="text-sm">You've reached the end of the results.</p>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
