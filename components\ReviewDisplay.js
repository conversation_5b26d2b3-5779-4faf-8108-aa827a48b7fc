import { useState } from "react";
import { formatDate } from "../lib/utils";
import { User, Calendar } from "lucide-react";
import StarRating from "./StarRating";

export default function ReviewDisplay({ 
  review, 
  showReviewer = true, 
  className = "",
  compact = false 
}) {
  const [expanded, setExpanded] = useState(false);
  
  if (!review) return null;

  const reviewText = review.review_text || "";
  const shouldTruncate = reviewText.length > 150 && compact;
  const displayText = shouldTruncate && !expanded 
    ? reviewText.substring(0, 150) + "..." 
    : reviewText;

  return (
    <div className={`border rounded-lg p-4 bg-white ${className}`}>
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3">
          {showReviewer && (
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                <User size={16} className="text-gray-600" />
              </div>
              <div>
                <p className="font-medium text-sm">
                  {review.reviewer_name || "Anonymous"}
                </p>
                <div className="flex items-center gap-1 text-xs text-gray-500">
                  <Calendar size={12} />
                  {formatDate(review.created_at)}
                </div>
              </div>
            </div>
          )}
        </div>
        <StarRating 
          rating={review.rating} 
          showCount={false} 
          size={14}
        />
      </div>

      {/* Review Text */}
      <div className="text-gray-700 text-sm leading-relaxed">
        <p>{displayText}</p>
        {shouldTruncate && (
          <button
            onClick={() => setExpanded(!expanded)}
            className="text-blue-600 hover:text-blue-800 text-xs mt-1 font-medium"
          >
            {expanded ? "Show less" : "Read more"}
          </button>
        )}
      </div>

      {/* Review metadata for compact view */}
      {compact && !showReviewer && (
        <div className="flex items-center gap-1 text-xs text-gray-500 mt-2">
          <Calendar size={12} />
          {formatDate(review.created_at)}
        </div>
      )}
    </div>
  );
}
