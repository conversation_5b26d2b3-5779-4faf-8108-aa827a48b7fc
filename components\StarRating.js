import { Star } from "lucide-react";

export default function StarRating({ 
  rating, 
  totalReviews = 0, 
  size = 16, 
  showCount = true, 
  className = "",
  interactive = false,
  onRatingChange = null,
  hoveredRating = 0,
  onHover = null,
  onLeave = null
}) {
  const stars = [];
  const displayRating = hoveredRating || rating || 0;
  
  for (let i = 1; i <= 5; i++) {
    stars.push(
      <Star
        key={i}
        size={size}
        className={`${
          i <= displayRating
            ? "fill-yellow-400 text-yellow-400"
            : "text-gray-300"
        } ${interactive ? "cursor-pointer hover:scale-110 transition-transform" : ""}`}
        onClick={interactive && onRatingChange ? () => onRatingChange(i) : undefined}
        onMouseEnter={interactive && onHover ? () => onHover(i) : undefined}
        onMouseLeave={interactive && onLeave ? onLeave : undefined}
      />
    );
  }

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      <div className="flex gap-0.5">
        {stars}
      </div>
      {showCount && totalReviews > 0 && (
        <span className="text-sm text-gray-600 ml-1">
          ({totalReviews} {totalReviews === 1 ? "review" : "reviews"})
        </span>
      )}
      {!showCount && rating > 0 && (
        <span className="text-sm text-gray-600 ml-1">
          {rating.toFixed(1)}
        </span>
      )}
    </div>
  );
}
