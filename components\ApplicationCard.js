import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { formatDate, formatCurrency, formatPercentage } from "../lib/utils";
import {
  FileText,
  Check,
  X,
  Clock,
  User,
  Building,
  Calendar,
  DollarSign,
  MessageSquare,
  ExternalLink,
  AlertCircle,
  MoreVertical,
  Edit,
  Eye,
  Star,
  Download,
  Shield
} from "lucide-react";

export default function ApplicationCard({ application, user, onUpdate, className = "" }) {
  const [acceptLoading, setAcceptLoading] = useState(false);
  const [rejectLoading, setRejectLoading] = useState(false);
  const [hasReview, setHasReview] = useState(false);
  const [reviewLoading, setReviewLoading] = useState(false);
  const router = useRouter();

  // Debug logging
  if (application.contract_id) {
    console.log(`Application ${application.id}:`, {
      app_status: application.status,
      contract_status: application.contract_status,
      contract_id: application.contract_id,
      offer_released: application.contract_status === "offer_released",
      should_show_completed: application.status === "completed" || application.contract_status === "offer_released"
    });
  }

  // Check if review exists for completed applications
  useEffect(() => {
    const checkReviewExists = async () => {
      if ((application.status === "completed" || application.contract_status === "offer_released") && application.id) {
        setReviewLoading(true);
        try {
          const res = await fetch(`/api/reviews/check?applicationId=${application.id}`);
          if (res.ok) {
            const data = await res.json();
            setHasReview(data.hasReviewed);
          }
        } catch (error) {
          console.error("Failed to check review status:", error);
        } finally {
          setReviewLoading(false);
        }
      }
    };

    checkReviewExists();
  }, [application.id, application.status, application.contract_status]);

  const handleAction = async (action) => {
    if (action === 'accept') {
      setAcceptLoading(true);
    } else if (action === 'reject') {
      setRejectLoading(true);
    }
    try {
      const res = await fetch(`/api/applications/${application.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ action }),
      });

      if (res.ok) {
        const data = await res.json();

        // If application was accepted and contract was generated, show success message and option to view contract
        if (action === 'accept' && data.contractGenerated && data.contractId) {
          const viewContract = window.confirm(
            "Application accepted successfully! A contract has been generated. Would you like to view the contract now?"
          );

          if (viewContract) {
            router.push(`/contract/${data.contractId}`);
            return;
          }
        } else if (action === 'accept') {
          alert("Application accepted successfully!");
        } else if (action === 'reject') {
          alert("Application rejected successfully!");
        }

        onUpdate?.();
      } else {
        const error = await res.json();
        alert(error.error || "Failed to update application");
        console.error("Failed to update application:", error);
      }
    } catch (error) {
      alert("Failed to update application");
      console.error("Failed to update application:", error);
    } finally {
      setAcceptLoading(false);
      setRejectLoading(false);
    }
  };

  const handleGenerateContract = async () => {
    setAcceptLoading(true);
    try {
      const res = await fetch("/api/contracts/create", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ applicationId: application.id }),
      });

      if (res.ok) {
        const data = await res.json();
        const viewContract = window.confirm(
          "Contract generated successfully! Would you like to view the contract now?"
        );

        if (viewContract) {
          router.push(`/contract/${data.contractId}`);
        } else {
          onUpdate?.(); // Refresh the application list
        }
      } else {
        const error = await res.json();
        // If contract already exists, still offer to view it
        if (error.contractId) {
          const viewContract = window.confirm(
            "Contract already exists for this application. Would you like to view it?"
          );
          if (viewContract) {
            router.push(`/contract/${error.contractId}`);
          }
        } else {
          alert(error.error || "Failed to generate contract");
        }
      }
    } catch (error) {
      alert("Failed to generate contract");
      console.error("Failed to generate contract:", error);
    } finally {
      setAcceptLoading(false);
    }
  };

  const handleDownloadContractPDF = async () => {
    try {
      const res = await fetch("/api/contracts/generate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ applicationId: application.id }),
      });

      if (res.ok) {
        const blob = await res.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `contract-${application.id}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        const error = await res.json();
        alert(error.error || "Failed to download contract");
      }
    } catch (error) {
      console.error("Failed to download contract:", error);
      alert("Failed to download contract");
    }
  };

  const getStatusBadge = () => {
    // For completed applications or offer released, always show "Completed"
    if (application.status === "completed" || application.contract_status === "offer_released") {
      return (
        <span className="status-completed flex items-center gap-1 mt-3">
          <Check size={12} />
          Completed
        </span>
      );
    }

    // If application is in_progress, show more specific status based on contract status
    if (application.status === "in_progress" && application.contract_status) {
      const contractStatusConfig = {
        pending_signatures: { className: "status-in-progress", icon: FileText, label: "Awaiting Signatures" },
        signed: { className: "status-in-progress", icon: FileText, label: "Documents Pending" },
        documents_pending: { className: "status-in-progress", icon: FileText, label: "Documents Pending" },
        documents_verified: { className: "status-accepted", icon: Check, label: "Documents Verified" },
        offer_released: { className: "status-completed", icon: Check, label: "Offer Released" },
      };

      const contractConfig = contractStatusConfig[application.contract_status];
      if (contractConfig) {
        const Icon = contractConfig.icon;
        return (
          <span className={`${contractConfig.className} flex items-center gap-1 mt-3`}>
            <Icon size={12} />
            {contractConfig.label}
          </span>
        );
      }
    }

    // Default status configuration
    const statusConfig = {
      pending: { className: "status-pending", icon: Clock, label: "Pending" },
      accepted: { className: "status-accepted", icon: Check, label: "Accepted" },
      rejected: { className: "status-rejected", icon: X, label: "Rejected" },
      in_progress: { className: "status-in-progress", icon: FileText, label: "In Progress" },
      completed: { className: "status-completed", icon: Check, label: "Completed" },
    };

    const config = statusConfig[application.status] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <span className={`${config.className} flex items-center gap-1 mt-3`}>
        <Icon size={12} />
        {config.label}
      </span>
    );
  };

  const getPaymentDisplay = () => {
    if (application.payment_type === "percentage") {
      return `${formatPercentage(application.payment_percentage)}% recurring`;
    }
    return `${formatCurrency(application.payment_fixed)} fixed`;
  };

  const getDaysAgo = () => {
    const days = Math.floor((new Date() - new Date(application.created_at)) / (1000 * 60 * 60 * 24));
    if (days === 0) return "Today";
    if (days === 1) return "Yesterday";
    return `${days} days ago`;
  };

  return (
    <div className={`card card-hover group ${className}`}>
      <div className="card-content">
        {/* Header */}
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1 min-w-0">
            <h4 className="text-lg font-semibold mb-1 truncate group-hover:text-primary transition-colors mt-3">
              {user.user_type === "seeker" ? (
                <span
                  className="cursor-pointer hover:text-blue-600 transition-colors"
                  onClick={() => application.referrer_user_id && router.push(`/user/${application.referrer_user_id}`)}
                >
                  {application.referrer_name || "Unknown Referrer"}
                </span>
              ) : (
                <div>
                  <div>{application.job_title || "Unknown Job"}</div>
                  {application.seeker_name && application.seeker_user_id && (
                    <div className="text-sm text-gray-600 mt-1">
                      by{" "}
                      <span
                        className="cursor-pointer hover:text-blue-600 transition-colors font-medium"
                        onClick={() => router.push(`/user/${application.seeker_user_id}`)}
                      >
                        {application.seeker_name}
                      </span>
                    </div>
                  )}
                </div>
              )}
            </h4>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Calendar size={14} />
              <span>{getDaysAgo()}</span>
              <span>•</span>
              <span>{formatDate(application.created_at)}</span>
            </div>
          </div>
          <div className="flex flex-col items-end gap-2">
            {getStatusBadge()}
            {application.status === "in_progress" && (
              <div className="text-xs text-red-600 bg-red-50 px-2 py-1 rounded mt-3">
                Documents & Verification
              </div>
            )}
          </div>
        </div>

        {/* Application Details */}
        <div className="space-y-3 mb-4">
          {user.user_type === "referrer" && application.seeker_name && (
            <div className="flex items-center gap-2 text-sm">
              <User size={14} className="text-muted-foreground" />
              <span className="text-muted-foreground">Job Seeker:</span>
              <span className="font-medium">{application.seeker_name}</span>
            </div>
          )}

          {application.company_name && (
            <div className="flex items-center gap-2 text-sm">
              <Building size={14} className="text-muted-foreground" />
              <span className="text-muted-foreground">Company:</span>
              <span className="font-medium">{application.company_name}</span>
            </div>
          )}

          {(application.payment_type || application.payment_percentage || application.payment_fixed) && (
            <div className="flex items-center gap-2 text-sm">
              <DollarSign size={14} className="text-muted-foreground" />
              <span className="text-muted-foreground">Payment:</span>
              <span className="font-medium">{getPaymentDisplay()}</span>
            </div>
          )}

          {application.job_role && (
            <div className="flex items-center gap-2">
              <span className="badge-secondary text-xs">{application.job_role}</span>
            </div>
          )}
        </div>

        {/* Message */}
        {application.message && (
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <MessageSquare size={14} className="text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">Message:</span>
            </div>
            <p className="text-sm bg-muted p-3 rounded-lg border-l-2 border-border">
              {application.message}
            </p>
          </div>
        )}

        {/* Position Details */}
        {application.position_details && (
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <FileText size={14} className="text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">Position Details:</span>
            </div>
            <p className="text-sm bg-muted p-3 rounded-lg">
              {application.position_details}
            </p>
          </div>
        )}

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-2 justify-between items-start">
          <div className="flex flex-wrap gap-2">
            {application.status === "pending" && user.user_type === "seeker" && (
              <>
                <button
                  onClick={() => handleAction("accept")}
                  disabled={acceptLoading || rejectLoading}
                  className="btn-primary btn-sm flex items-center gap-2"
                >
                  {acceptLoading ? (
                    <div className="w-3 h-3 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Check size={14} />
                  )}
                  Accept
                </button>
                <button
                  onClick={() => handleAction("reject")}
                  disabled={acceptLoading || rejectLoading}
                  className="btn-outline btn-sm flex items-center gap-2 text-destructive border-destructive hover:bg-destructive hover:text-destructive-foreground"
                >
                  {rejectLoading ? (
                    <div className="w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <X size={14} />
                  )}
                  Reject
                </button>
              </>
            )}

            {/* Contract Access Buttons - For Accepted Applications */}
            {application.status === "accepted" && application.contract_id && (
              <button
                onClick={() => router.push(`/contract/${application.contract_id}`)}
                className="btn-primary btn-sm flex items-center gap-2"
              >
                <ExternalLink size={14} />
                View Contract
              </button>
            )}

            {application.status === "accepted" && !application.contract_id && (
              <button
                onClick={() => handleGenerateContract()}
                disabled={acceptLoading}
                className="btn-primary btn-sm flex items-center gap-2"
              >
                {acceptLoading ? (
                  <div className="w-3 h-3 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin" />
                ) : (
                  <FileText size={14} />
                )}
                Generate Contract
              </button>
            )}

            {/* Contract Access Buttons - For Completed Applications (check this first) */}
            {(application.status === "completed" || application.contract_status === "offer_released") && application.contract_id ? (
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => router.push(`/contract/${application.contract_id}`)}
                  className="btn-outline btn-sm flex items-center gap-2"
                >
                  <FileText size={14} />
                  View Contract
                </button>
                <button
                  onClick={() => router.push(`/contract/${application.contract_id}?tab=documents`)}
                  className="btn-outline btn-sm flex items-center gap-2"
                >
                  <Shield size={14} />
                  View Documents
                </button>
                <button
                  onClick={handleDownloadContractPDF}
                  className="btn-outline btn-sm flex items-center gap-2"
                >
                  <Download size={14} />
                  Download PDF
                </button>
                <button
                  onClick={() => router.push(`/contract/${application.contract_id}?tab=review`)}
                  className="btn-primary btn-sm flex items-center gap-2"
                  disabled={reviewLoading}
                >
                  <Star size={14} />
                  {reviewLoading ? "Checking..." : hasReview ? "View Review" : "Leave Review"}
                </button>
              </div>
            ) :
            /* Contract Access Buttons - For In Progress Applications */
            application.status === "in_progress" && application.contract_id ? (
              <div className="flex flex-col sm:flex-row gap-2">
                {/* Show different buttons based on contract status and signature status */}
                {(application.contract_status === "pending_signatures" || !application.contract_status) && (
                  (() => {
                    // Check if current user has already signed
                    const isSeeker = user.user_type === "seeker";
                    const currentUserSigned = isSeeker ? application.seeker_signed : application.referrer_signed;
                    const otherPartySigned = isSeeker ? application.referrer_signed : application.seeker_signed;

                    // If both have signed, don't show sign button (this should be handled by contract_status being "signed")
                    if (currentUserSigned && otherPartySigned) {
                      return null;
                    }

                    // If current user hasn't signed, show sign button
                    if (!currentUserSigned) {
                      return (
                        <button
                          onClick={() => router.push(`/contract/${application.contract_id}`)}
                          className="btn-primary btn-sm flex items-center justify-center gap-2 w-full sm:w-auto"
                        >
                          <FileText size={14} />
                          {!application.contract_status ? "Continue Contract" : "Sign Contract"}
                        </button>
                      );
                    }

                    // If current user has signed but other party hasn't, show waiting message
                    if (currentUserSigned && !otherPartySigned) {
                      return (
                        <div className="flex items-center gap-2 text-sm text-orange-600">
                          <Clock size={14} />
                          <span>Waiting for other party to sign</span>
                        </div>
                      );
                    }

                    return null;
                  })()
                )}

                {application.contract_status === "signed" && (
                  <div className="flex gap-2 w-full sm:w-auto">
                    <button
                      onClick={() => router.push(`/contract/${application.contract_id}`)}
                      className="btn-primary btn-sm flex items-center justify-center gap-2 flex-1 sm:flex-none"
                    >
                      <FileText size={14} />
                      View Contract
                    </button>
                    <button
                      onClick={() => router.push(`/contract/${application.contract_id}?tab=documents`)}
                      className="btn-outline btn-sm flex items-center justify-center gap-2 flex-1 sm:flex-none"
                      title="Verify Documents"
                    >
                      <ExternalLink size={14} />
                      Documents
                    </button>
                  </div>
                )}

                {application.contract_status === "documents_pending" && (
                  <div className="flex gap-2 w-full sm:w-auto">
                    <button
                      onClick={() => router.push(`/contract/${application.contract_id}`)}
                      className="btn-primary btn-sm flex items-center justify-center gap-2 flex-1 sm:flex-none"
                    >
                      <FileText size={14} />
                      View Contract
                    </button>
                    <button
                      onClick={() => router.push(`/contract/${application.contract_id}?tab=documents`)}
                      className="btn-outline btn-sm flex items-center justify-center gap-2 flex-1 sm:flex-none"
                      title="Document Verification"
                    >
                      <ExternalLink size={14} />
                      Documents
                    </button>
                  </div>
                )}

                {application.contract_status === "documents_verified" && (
                  <div className="flex gap-2 w-full sm:w-auto">
                    <button
                      onClick={() => router.push(`/contract/${application.contract_id}`)}
                      className="btn-primary btn-sm flex items-center justify-center gap-2 flex-1 sm:flex-none"
                    >
                      <FileText size={14} />
                      View Contract
                    </button>
                    <button
                      onClick={() => router.push(`/contract/${application.contract_id}?tab=documents`)}
                      className="btn-outline btn-sm flex items-center justify-center gap-2 flex-1 sm:flex-none"
                      title="View Documents"
                    >
                      <Eye size={14} />
                      Documents
                    </button>
                    <div className="flex items-center gap-2 text-sm text-green-600">
                      <Check size={14} />
                      <span>Documents Verified</span>
                    </div>
                  </div>
                )}

                {application.contract_status === "offer_released" && application.status !== "completed" && (
                  <div className="flex gap-2 w-full sm:w-auto">
                    <button
                      onClick={() => router.push(`/contract/${application.contract_id}`)}
                      className="btn-primary btn-sm flex items-center justify-center gap-2 flex-1 sm:flex-none"
                    >
                      <FileText size={14} />
                      View Contract
                    </button>
                    <div className="flex items-center gap-2 text-sm text-green-600">
                      <Check size={14} />
                      <span>Offer Released</span>
                    </div>
                  </div>
                )}

                {/* Fallback for other contract statuses or undefined status */}
                {application.contract_status && !["pending_signatures", "signed", "documents_pending", "documents_verified", "offer_released"].includes(application.contract_status) && (
                  <button
                    onClick={() => router.push(`/contract/${application.contract_id}`)}
                    className="btn-primary btn-sm flex items-center justify-center gap-2 w-full sm:w-auto"
                  >
                    <FileText size={14} />
                    Continue Contract
                  </button>
                )}
              </div>
            ) : null}

            {/* Contract Pending State */}
            {application.status === "accepted" && !application.contract_id && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <AlertCircle size={14} />
                <span>Contract pending</span>
              </div>
            )}

            {/* Missing Contract for In Progress */}
            {application.status === "in_progress" && !application.contract_id && (
              <div className="flex gap-2">
                <button
                  onClick={() => handleGenerateContract()}
                  disabled={acceptLoading}
                  className="btn-primary btn-sm flex items-center gap-2"
                >
                  {acceptLoading ? (
                    <div className="w-3 h-3 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <FileText size={14} />
                  )}
                  Generate Contract
                </button>
                <div className="flex items-center gap-2 text-sm text-orange-600">
                  <AlertCircle size={14} />
                  <span>Contract missing</span>
                </div>
              </div>
            )}
          </div>

          {/* Additional Info */}
          <div className="text-xs text-muted-foreground">
            ID: {application.id}
          </div>
        </div>
      </div>
    </div>
  );
}
