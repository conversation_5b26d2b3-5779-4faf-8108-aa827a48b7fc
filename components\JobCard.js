import { useState } from "react";
import { useRouter } from "next/router";
import { formatCurrency, formatDate, formatPercentage } from "../lib/utils";
import {
  Eye,
  EyeOff,
  Share2,
  Users,
  DollarSign,
  Calendar,
  MapPin,
  Clock,
  ExternalLink,
  CheckCircle
} from "lucide-react";
import StarRating from "./StarRating";

export default function JobCard({ job, user, isOwner = false, className = "" }) {
  const router = useRouter();
  const [copied, setCopied] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleShare = async () => {
    const url = `${window.location.origin}/job/${job.shareable_link || job.id}`;
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const handleViewDetails = () => {
    setIsLoading(true);
    router.push(`/job/${job.id}`);
  };

  const isAnonymous = job.visibility === "anonymous";
  const displayName = isAnonymous && !isOwner ? "Anonymous" : job.user_name || job.full_name;

  const getStatusBadge = () => {
    switch (job.status) {
      case 'active':
        return <span className="status-accepted mt-3">Active</span>;
      case 'paused':
        return <span className="status-pending mt-3">Paused</span>;
      case 'closed':
        return <span className="status-rejected mt-3">Closed</span>;
      default:
        return null;
    }
  };

  const getVisibilityIcon = () => {
    if (job.visibility === "private") {
      return <EyeOff size={16} className="text-muted-foreground" title="Private" />;
    }
    if (job.visibility === "anonymous") {
      return <Eye size={16} className="text-muted-foreground" title="Anonymous" />;
    }
    return null;
  };

  return (
    <div className={`card card-hover group ${className}`}>
      <div className="card-content">
        {/* Header */}
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="text-lg font-semibold truncate group-hover:text-primary transition-colors mt-4">
                {job.title}
              </h3>
              {getVisibilityIcon()}
              {getStatusBadge()}
            </div>
            <p className="text-muted-foreground text-sm line-clamp-2 leading-relaxed">
              {job.description}
            </p>
          </div>
        </div>

        {/* Job Details Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Users size={14} className="flex-shrink-0" />
            <div className="flex items-center gap-2 min-w-0">
              <span className="truncate cursor-pointer hover:text-blue-600 transition-colors"
                    onClick={() => !isAnonymous && router.push(`/user/${job.user_id}`)}>
                {displayName}
              </span>
              {!isAnonymous && job.total_reviews > 0 && (
                <StarRating
                  rating={parseFloat(job.average_rating)}
                  totalReviews={job.total_reviews}
                  size={12}
                  showCount={false}
                />
              )}
            </div>
          </div>

          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <DollarSign size={14} className="flex-shrink-0" />
            <span className="truncate">
              {job.payment_type === "percentage"
                ? `${formatPercentage(job.payment_percentage)}% recurring`
                : formatCurrency(job.payment_fixed)}
            </span>
          </div>

          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Calendar size={14} className="flex-shrink-0" />
            <span className="truncate">{formatDate(job.created_at)}</span>
          </div>

          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Clock size={14} className="flex-shrink-0" />
            <span className="truncate">{job.experience_years || 0}+ years exp</span>
          </div>
        </div>

        {/* Job Role and Skills */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-2">
            <span className="badge-secondary text-xs">{job.job_role}</span>
            {job.skills && job.skills.split(',').slice(0, 3).map((skill, index) => (
              <span key={index} className="badge-outline text-xs">
                {skill.trim()}
              </span>
            ))}
            {job.skills && job.skills.split(',').length > 3 && (
              <span className="text-xs text-muted-foreground">
                +{job.skills.split(',').length - 3} more
              </span>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
          <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
            <button
              onClick={handleViewDetails}
              disabled={isLoading}
              className="btn-primary btn-sm flex items-center justify-center gap-2 w-full sm:w-auto"
            >
              {isLoading ? (
                <div className="w-4 h-4 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin" />
              ) : (
                <ExternalLink size={14} />
              )}
              View Details
            </button>

            {job.visibility !== "private" && (
              <button
                onClick={handleShare}
                className="btn-outline btn-sm flex items-center justify-center gap-2 w-full sm:w-auto"
              >
                {copied ? (
                  <>
                    <CheckCircle size={14} />
                    Copied!
                  </>
                ) : (
                  <>
                    <Share2 size={14} />
                    Share
                  </>
                )}
              </button>
            )}
          </div>

          <div className="flex items-center gap-4 text-xs text-muted-foreground">
            <span>{job.applications_count || 0} applications</span>
            {job.desired_companies && (
              <span className="hidden sm:inline truncate max-w-32" title={job.desired_companies}>
                Target: {job.desired_companies}
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
