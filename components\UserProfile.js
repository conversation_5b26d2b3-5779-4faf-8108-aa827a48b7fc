import { useState, useEffect } from "react";
import { User, Calendar, MapPin, Linkedin, Github, Globe, Star } from "lucide-react";
import { formatDate } from "../lib/utils";
import StarRating from "./StarRating";
import ReviewDisplay from "./ReviewDisplay";

export default function UserProfile({ userId, className = "" }) {
  const [userData, setUserData] = useState(null);
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [reviewsLoading, setReviewsLoading] = useState(false);
  const [pagination, setPagination] = useState(null);
  const [ratingDistribution, setRatingDistribution] = useState({});
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    if (userId) {
      fetchUserData(1);
    }
  }, [userId]);

  const fetchUserData = async (page = 1) => {
    if (page === 1) {
      setLoading(true);
    } else {
      setReviewsLoading(true);
    }

    try {
      const res = await fetch(`/api/users/${userId}/reviews?page=${page}&limit=5`);
      if (res.ok) {
        const data = await res.json();
        setUserData(data.user);
        
        if (page === 1) {
          setReviews(data.reviews);
        } else {
          setReviews(prev => [...prev, ...data.reviews]);
        }
        
        setPagination(data.pagination);
        setRatingDistribution(data.ratingDistribution);
        setCurrentPage(page);
      }
    } catch (error) {
      console.error("Failed to fetch user data:", error);
    } finally {
      setLoading(false);
      setReviewsLoading(false);
    }
  };

  const loadMoreReviews = () => {
    if (pagination?.hasNextPage) {
      fetchUserData(currentPage + 1);
    }
  };

  if (loading) {
    return (
      <div className={`card ${className}`}>
        <div className="card-content text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (!userData) {
    return (
      <div className={`card ${className}`}>
        <div className="card-content text-center py-8">
          <p className="text-gray-600">User not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* User Info Card */}
      <div className="card">
        <div className="card-content">
          <div className="flex items-start gap-4 mb-6 mt-5">
            <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
              <User size={32} className="text-gray-600" />
            </div>
            <div className="flex-1">
              <h1 className="text-2xl font-bold mb-2">{userData.full_name}</h1>
              <p className="text-gray-600 capitalize mb-2">
                {userData.user_type === "seeker" ? "Job Seeker" : "Referrer"}
              </p>
              
              {/* Rating Summary */}
              {userData.total_reviews > 0 && (
                <div className="mb-3">
                  <StarRating 
                    rating={parseFloat(userData.average_rating)} 
                    totalReviews={userData.total_reviews}
                    size={18}
                  />
                </div>
              )}

              <div className="flex items-center gap-1 text-sm text-gray-500">
                <Calendar size={14} />
                <span>Member since {formatDate(userData.created_at)}</span>
              </div>
            </div>
          </div>

          {/* Bio */}
          {userData.bio && (
            <div className="mb-6">
              <h3 className="font-semibold mb-2">About</h3>
              <p className="text-gray-700 leading-relaxed">{userData.bio}</p>
            </div>
          )}

          {/* Links */}
          {(userData.linkedin_url || userData.github_url || userData.portfolio_url) && (
            <div>
              <h3 className="font-semibold mb-3">Links</h3>
              <div className="flex gap-4">
                {userData.linkedin_url && (
                  <a
                    href={userData.linkedin_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 text-blue-600 hover:underline"
                  >
                    <Linkedin size={16} />
                    LinkedIn
                  </a>
                )}
                {userData.github_url && (
                  <a
                    href={userData.github_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 text-gray-800 hover:underline"
                  >
                    <Github size={16} />
                    GitHub
                  </a>
                )}
                {userData.portfolio_url && (
                  <a
                    href={userData.portfolio_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 text-purple-600 hover:underline"
                  >
                    <Globe size={16} />
                    Portfolio
                  </a>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Reviews Section */}
      {userData.total_reviews > 0 && (
        <div className="card">
          <div className="card-content">
            <div className="flex items-center justify-between mb-6 mt-5">
              <h2 className="text-xl font-semibold">
                Reviews ({userData.total_reviews})
              </h2>
              <StarRating 
                rating={parseFloat(userData.average_rating)} 
                showCount={false}
                size={16}
              />
            </div>

            {/* Rating Distribution */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="font-medium mb-3">Rating Distribution</h3>
              <div className="space-y-2">
                {[5, 4, 3, 2, 1].map(rating => (
                  <div key={rating} className="flex items-center gap-3">
                    <span className="text-sm w-8">{rating} ★</span>
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-yellow-400 h-2 rounded-full"
                        style={{ 
                          width: `${userData.total_reviews > 0 ? (ratingDistribution[rating] / userData.total_reviews) * 100 : 0}%` 
                        }}
                      />
                    </div>
                    <span className="text-sm text-gray-600 w-8">
                      {ratingDistribution[rating] || 0}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Reviews List */}
            <div className="space-y-4">
              {reviews.map((review) => (
                <ReviewDisplay 
                  key={review.id} 
                  review={review} 
                  showReviewer={true}
                  compact={true}
                />
              ))}
            </div>

            {/* Load More Button */}
            {pagination?.hasNextPage && (
              <div className="text-center mt-6">
                <button
                  onClick={loadMoreReviews}
                  disabled={reviewsLoading}
                  className="btn-outline"
                >
                  {reviewsLoading ? "Loading..." : "Load More Reviews"}
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* No Reviews */}
      {userData.total_reviews === 0 && (
        <div className="card">
          <div className="card-content text-center py-8">
            <Star size={48} className="mx-auto mb-4 text-gray-300" />
            <h3 className="text-lg font-medium mb-2">No Reviews Yet</h3>
            <p className="text-gray-600">
              This user hasn't received any reviews yet.
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
