import { withAuth } from "../../../lib/middleware";
import { query } from "../../../lib/db";

export default withAuth(async (req, res) => {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { applicationId, rating, reviewText } = req.body;

  try {
    // Verify the application is completed
    const applications = await query(
      `
      SELECT
        a.*,
        jp.user_id as seeker_id,
        c.offer_letter_released,
        c.seeker_signed,
        c.referrer_signed
      FROM applications a
      JOIN job_posts jp ON a.job_post_id = jp.id
      LEFT JOIN contracts c ON c.application_id = a.id
      WHERE a.id = ? AND (
        a.status = 'completed' OR
        (c.offer_letter_released = TRUE AND c.seeker_signed = TRUE AND c.referrer_signed = TRUE)
      )
    `,
      [applicationId]
    );

    if (applications.length === 0) {
      return res
        .status(400)
        .json({
          error: "Application not found or not ready for review",
          debug: { applicationId, message: "Application must be completed or have offer letter released with both signatures" }
        });
    }

    const app = applications[0];

    // Determine who is being reviewed
    const reviewedUserId =
      req.user.id === app.seeker_id ? app.referrer_id : app.seeker_id;

    // Check if already reviewed
    const existing = await query(
      `SELECT id FROM reviews 
       WHERE application_id = ? AND reviewer_id = ? AND reviewed_user_id = ?`,
      [applicationId, req.user.id, reviewedUserId]
    );

    if (existing.length > 0) {
      return res.status(400).json({ error: "Already reviewed" });
    }

    // Create review
    await query(
      `INSERT INTO reviews (
        application_id, reviewer_id, reviewed_user_id, rating, review_text
      ) VALUES (?, ?, ?, ?, ?)`,
      [applicationId, req.user.id, reviewedUserId, rating, reviewText]
    );

    res.status(201).json({ success: true });
  } catch (error) {
    console.error("Failed to create review:", error);
    res.status(500).json({ error: "Failed to create review" });
  }
});
