-- Fix review access by ensuring application status is completed
USE u618120801_SplitJob_33;

-- Check current status before fix
SELECT 'Before Fix - Application Status:' as info;
SELECT 
    a.id as app_id,
    a.status as app_status,
    c.offer_letter_released,
    c.seeker_signed,
    c.referrer_signed,
    CASE 
        WHEN a.status = 'completed' THEN 'Review Available'
        WHEN c.offer_letter_released = 1 AND c.seeker_signed = 1 AND c.referrer_signed = 1 THEN 'Should be completed'
        ELSE 'Not ready for review'
    END as review_status
FROM applications a
LEFT JOIN contracts c ON c.application_id = a.id
WHERE a.id IN (1, 2, 3, 6);

-- Update applications to completed where offer letter is released and both signed
UPDATE applications a
JOIN contracts c ON c.application_id = a.id
SET a.status = 'completed'
WHERE c.offer_letter_released = 1 
  AND c.seeker_signed = 1 
  AND c.referrer_signed = 1
  AND a.status != 'completed';

-- Check status after fix
SELECT 'After Fix - Application Status:' as info;
SELECT 
    a.id as app_id,
    a.status as app_status,
    c.offer_letter_released,
    c.seeker_signed,
    c.referrer_signed,
    CASE 
        WHEN a.status = 'completed' THEN 'Review Available'
        ELSE 'Review Not Available'
    END as review_status
FROM applications a
LEFT JOIN contracts c ON c.application_id = a.id
WHERE a.id IN (1, 2, 3, 6);
