import { useState, useEffect, useCallback } from 'react';

export default function useInfiniteScroll({
  fetchMore,
  hasNextPage,
  threshold = 100
}) {
  const [isFetching, setIsFetching] = useState(false);

  const handleScroll = useCallback(() => {
    if (
      window.innerHeight + document.documentElement.scrollTop + threshold >=
      document.documentElement.offsetHeight
    ) {
      if (hasNextPage && !isFetching) {
        setIsFetching(true);
      }
    }
  }, [hasNextPage, isFetching, threshold]);

  useEffect(() => {
    if (isFetching && hasNextPage) {
      fetchMore().finally(() => {
        setIsFetching(false);
      });
    }
  }, [isFetching, hasNextPage, fetchMore]);

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  return { isFetching };
}
